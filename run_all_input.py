import os
import glob
import subprocess

# Get all .md files in ./output/
md_files = glob.glob(os.path.join('../scale_up_dry_run_papers', '*.md')) ## change input source

if not md_files:
    print('No .md files found in ./output/')
    exit(0)

for md_file in md_files:
    print(f'Processing {md_file}...')
    cmd = [
        'python',
        'src/extraction_pipeline_parallel_strurcture.py',
        '--input-file',
        md_file,
        '--output-dir', ## change output source
        '../scale_up_dry_run_papers_results'
    ]
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print(f'Error processing {md_file}, exited with code {result.returncode}')
        break
print('Done.')
