#!/usr/bin/env python3
"""
Script to convert XML files to markdown and organize them into batches for processing.
"""

import os
import shutil
from pathlib import Path
from lxml import etree
import markdownify

def extract_title(tree):
    title_el = tree.find(".//article-title")
    return f"# {title_el.text.strip()}" if title_el is not None and title_el.text else ""

def extract_abstract(tree):
    abs_el = tree.find(".//abstract")
    if abs_el is not None:
        paragraphs = []
        for p in abs_el.xpath(".//p"):
            text = etree.tostring(p, encoding="unicode", method="html")
            text = markdownify.markdownify(text)
            if text.strip():
                paragraphs.append(f"<!-- PARAGRAPH_START [abstract] -->{text.strip()}")
        abs_text = "\n\n".join(paragraphs)
        return f"## Abstract\n{abs_text}"
    return ""

def extract_structured_content(tree):
    """Extract all content with section hierarchy preserved, with source tracking."""
    result = []
    
    # Process all top-level sections
    for sec in tree.xpath("//sec[not(ancestor::sec)]"):
        section_content = process_section(sec, 2)  # Start at h2 level
        result.append(section_content)
    
    # Get any paragraphs that aren't in sections or abstract
    outside_paras = []
    for p in tree.xpath("//p[not(ancestor::sec) and not(ancestor::abstract)]"):
        text = etree.tostring(p, encoding="unicode", method="html")
        text = markdownify.markdownify(text)
        if text.strip():
            outside_paras.append(f"<!-- PARAGRAPH_START [outside] -->{text.strip()}")
    
    if outside_paras:
        result.append("## Other Content\n" + "\n\n".join(outside_paras))
    
    return "\n\n".join(result)

def process_section(sec, level):
    """Process a section and its descendants recursively, with source tracking."""
    result = []
    
    # Extract title
    title_el = sec.find("title")
    if title_el is not None and title_el.text:
        title = f"{'#' * level} {title_el.text.strip()}"
        result.append(title)
    
    # Extract all paragraphs directly in this section (not in subsections)
    for p in sec.xpath("./p"):
        text = etree.tostring(p, encoding="unicode", method="html")
        text = markdownify.markdownify(text)
        if text.strip():
            result.append(f"<!-- PARAGRAPH_START [direct] -->{text.strip()}")
    
    # Extract paragraphs from other elements directly in this section
    for el in sec.xpath("./*[not(self::sec) and not(self::p) and not(self::title)]"):
        for p in el.xpath(".//p"):
            text = etree.tostring(p, encoding="unicode", method="html")
            text = markdownify.markdownify(text)
            if text.strip():
                result.append(f"<!-- PARAGRAPH_START [nested] -->{text.strip()}")
    
    # Process subsections recursively
    for subsec in sec.xpath("./sec"):
        subsection_content = process_section(subsec, level + 1)
        result.append(subsection_content)
    
    return "\n\n".join(result)

def extract_tables(tree):
    tables = tree.findall(".//table-wrap")
    output = []
    for i, table_wrap in enumerate(tables, 1):
        caption_el = table_wrap.find(".//caption")
        caption = (
            etree.tostring(caption_el, method="text", encoding="unicode").strip()
            if caption_el is not None else f"Table {i}"
        )
        table_el = table_wrap.find(".//table")
        if table_el is None:
            output.append(f"### {caption}\n\n_Table not found or unstructured._")
            continue
        rows = []
        for row in table_el.findall(".//tr"):
            cells = [
                etree.tostring(cell, method="text", encoding="unicode").strip()
                for cell in row.findall("./td") + row.findall("./th")
            ]
            rows.append(cells)
        if not rows:
            output.append(f"### {caption}\n\n_Table empty._")
            continue
        header = "| " + " | ".join(rows[0]) + " |"
        separator = "| " + " | ".join("---" for _ in rows[0]) + " |"
        body = "\n".join("| " + " | ".join(row) + " |" for row in rows[1:])
        table_md = f"<!-- TABLE_START -->\n### {caption}\n\n{header}\n{separator}\n{body}"
        output.append(table_md)
    return "\n\n".join(output)

def extract_references(tree):
    refs = tree.findall(".//ref-list/ref")
    output = []
    for i, ref in enumerate(refs, 1):
        label = ref.findtext("label") or f"[{i}]"
        mixed = ref.find("mixed-citation")
        if mixed is not None:
            ref_text = etree.tostring(mixed, method="text", encoding="unicode").strip()
        else:
            parts = []
            name = ref.find(".//name")
            if name is not None:
                surname = name.findtext("surname", "")
                given = name.findtext("given-names", "")
                parts.append(f"{surname} {given}")
            parts.append(ref.findtext(".//article-title", ""))
            parts.append(ref.findtext(".//source", ""))
            year = ref.findtext(".//year")
            if year:
                parts.append(f"({year})")
            ref_text = ". ".join(p for p in parts if p)
        output.append(f"- {label} {ref_text}")
    return "## References\n" + "\n".join(output) if output else ""

def convert_xml_to_md(xml_path, md_path):
    """Convert a single XML file to markdown"""
    tree = etree.parse(xml_path)
    root = tree.getroot()
    
    # Extract content
    title = extract_title(root)
    abstract = extract_abstract(root)
    structured_content = extract_structured_content(root)
    tables = extract_tables(root)
    references = extract_references(root)
    
    # Combine all parts
    parts = [title, abstract, structured_content, tables, references]
    md_content = "\n\n".join(filter(None, parts)).strip()
    md_path.write_text(md_content, encoding="utf-8")
    
    return True

def create_batch_directories():
    """Create 5 batch directories"""
    batch_dirs = []
    for i in range(1, 6):
        batch_dir = Path(f"markdown_batch_{i}")
        if batch_dir.exists():
            shutil.rmtree(batch_dir)
        batch_dir.mkdir()
        batch_dirs.append(batch_dir)
        print(f"Created directory: {batch_dir}")
    return batch_dirs

def get_all_xml_files():
    """Get all XML files from both articles and reviews directories"""
    xml_files = []
    
    # Get articles
    articles_dir = Path("xml_articles")
    if articles_dir.exists():
        xml_files.extend(list(articles_dir.glob("*.xml")))
    
    # Get reviews
    reviews_dir = Path("xml_reviews")
    if reviews_dir.exists():
        xml_files.extend(list(reviews_dir.glob("*.xml")))
    
    return sorted(xml_files)

def main():
    """Main processing function"""
    print("Starting XML to Markdown conversion and batching...")
    
    # Create batch directories
    batch_dirs = create_batch_directories()
    
    # Get all XML files
    xml_files = get_all_xml_files()
    print(f"Found {len(xml_files)} XML files to process")
    
    if len(xml_files) == 0:
        print("No XML files found!")
        return
    
    # Process files in batches of 20
    batch_size = 20
    processed_count = 0
    failed_count = 0
    
    for batch_idx, batch_dir in enumerate(batch_dirs):
        start_idx = batch_idx * batch_size
        end_idx = min(start_idx + batch_size, len(xml_files))
        batch_files = xml_files[start_idx:end_idx]
        
        print(f"\n=== Processing Batch {batch_idx + 1} ===")
        print(f"Files {start_idx + 1} to {end_idx} ({len(batch_files)} files)")
        
        for xml_file in batch_files:
            try:
                # Create markdown filename
                md_filename = xml_file.stem + ".md"
                md_path = batch_dir / md_filename
                
                # Convert XML to markdown
                convert_xml_to_md(xml_file, md_path)
                processed_count += 1
                print(f"  ✅ {xml_file.name} → {md_path}")
                
            except Exception as e:
                failed_count += 1
                print(f"  ❌ Failed to process {xml_file.name}: {e}")
    
    print(f"\n=== Summary ===")
    print(f"Total files processed: {processed_count}")
    print(f"Failed conversions: {failed_count}")
    print(f"Batch directories created: {len(batch_dirs)}")
    
    # Show batch contents
    for i, batch_dir in enumerate(batch_dirs, 1):
        md_files = list(batch_dir.glob("*.md"))
        print(f"Batch {i}: {len(md_files)} markdown files")

if __name__ == "__main__":
    main()
