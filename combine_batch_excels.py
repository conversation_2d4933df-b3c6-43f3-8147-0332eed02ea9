import pandas as pd
import os
import sys
import traceback
import argparse
from pathlib import Path
import warnings

def check_data_loss(original_dfs, combined_df, sheet_name):
    """Check for potential data loss when combining dataframes."""
    issues = []
    
    # Check row counts
    total_original_rows = sum(len(df) for df in original_dfs)
    combined_rows = len(combined_df)
    
    if total_original_rows != combined_rows:
        issues.append(f"Row count mismatch: Expected {total_original_rows}, got {combined_rows}")
    
    # Check column consistency
    all_columns = set()
    for df in original_dfs:
        all_columns.update(df.columns)
    
    if len(all_columns) != len(combined_df.columns):
        missing_cols = all_columns - set(combined_df.columns)
        if missing_cols:
            issues.append(f"Missing columns in combined data: {', '.join(missing_cols)}")
    
    # Check for duplicate rows that might have been dropped
    duplicates = combined_df.duplicated().sum()
    if duplicates > 0:
        issues.append(f"Found {duplicates} duplicate rows that might be dropped")
    
    return issues

def combine_excel_files(file_paths, output_path):
    """Combine multiple Excel files into one with data loss detection."""
    all_sheets = {}
    original_data = {}  # Store original dataframes for data loss detection
    
    # Process each file
    for file_path in file_paths:
        print(f"Reading file: {file_path}")
        try:
            if not os.path.exists(file_path):
                print(f"  - ERROR: File does not exist: {file_path}")
                continue
                
            # Read all sheets from the Excel file
            excel_data = pd.read_excel(file_path, sheet_name=None)
            
            if not excel_data:
                print(f"  - WARNING: No sheets found in {file_path}")
                continue
                
            # Add each sheet to the combined dictionary
            for sheet_name, df in excel_data.items():
                print(f"  - Found sheet: {sheet_name} ({len(df)} rows)")
                
                # Store original dataframe for later comparison
                if sheet_name not in original_data:
                    original_data[sheet_name] = []
                original_data[sheet_name].append(df)
                
                if sheet_name in all_sheets:
                    # Append to existing dataframe
                    all_sheets[sheet_name] = pd.concat([all_sheets[sheet_name], df], ignore_index=True)
                else:
                    # Create new entry
                    all_sheets[sheet_name] = df
                    
        except Exception as e:
            print(f"  - ERROR reading file {file_path}: {str(e)}")
            print(f"  - Detailed error: {traceback.format_exc()}")
    
    if not all_sheets:
        print("\nERROR: No data was collected from any of the input files.")
        return False
    
    # Check for data loss
    print("\nChecking for potential data loss issues...")
    data_loss_detected = False
    for sheet_name, combined_df in all_sheets.items():
        issues = check_data_loss(original_data[sheet_name], combined_df, sheet_name)
        if issues:
            data_loss_detected = True
            print(f"  - WARNING: Potential data loss in sheet '{sheet_name}':")
            for issue in issues:
                print(f"    * {issue}")
    
    if data_loss_detected:
        print("\nWARNING: Potential data loss detected. The combined file may not contain all original data.")
        user_input = input("Do you want to continue anyway? (y/n): ").strip().lower()
        if user_input != 'y':
            print("Operation cancelled by user.")
            return False
        
    # Write combined data to a new Excel file
    print(f"\nWriting combined data to {output_path}")
    try:
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            for sheet_name, df in all_sheets.items():
                print(f"  - Writing sheet: {sheet_name} ({len(df)} rows)")
                df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        # Verify the file was created
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"\nSUCCESS: Combined Excel file created: {output_path} ({file_size} bytes)")
            print("\nSummary of combined data:")
            for sheet_name, df in all_sheets.items():
                print(f"  - {sheet_name}: {len(df)} rows")
            return True
        else:
            print(f"\nERROR: Output file was not created: {output_path}")
            return False
            
    except Exception as e:
        print(f"\nERROR writing to {output_path}: {str(e)}")
        print(f"Detailed error: {traceback.format_exc()}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Combine multiple Excel files")
    parser.add_argument("--files", nargs='+', required=True, 
                        help="List of Excel files to combine")
    parser.add_argument("--output", type=str, default="combined_results.xlsx",
                        help="Output Excel file path")
    parser.add_argument("--force", action="store_true",
                        help="Force combination even if data loss is detected")
    
    args = parser.parse_args()
    
    # Check if output directory exists
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir)
            print(f"Created output directory: {output_dir}")
        except Exception as e:
            print(f"ERROR: Cannot create output directory {output_dir}: {str(e)}")
            return
    
    # Verify input files exist
    existing_files = []
    for file in args.files:
        if os.path.exists(file):
            existing_files.append(file)
        else:
            print(f"WARNING: File does not exist: {file}")
    
    if not existing_files:
        print("ERROR: None of the specified input files exist.")
        return
    
    print(f"Found {len(existing_files)} valid input files out of {len(args.files)} specified.")
    
    # Verify we can write to the output location
    try:
        output_dir = os.path.dirname(args.output) or '.'
        test_file = os.path.join(output_dir, '.write_test')
        with open(test_file, 'w') as f:
            f.write('test')
        os.remove(test_file)
    except Exception as e:
        print(f"ERROR: Cannot write to output directory {output_dir}: {str(e)}")
        return
    
    # Combine the files
    success = combine_excel_files(existing_files, args.output)
    
    if success:
        print("\nOperation completed successfully.")
    else:
        print("\nOperation failed. Please check the error messages above.")

if __name__ == "__main__":
    # Suppress pandas warnings
    warnings.simplefilter(action='ignore', category=FutureWarning)
    pd.options.mode.chained_assignment = None
    
    print("\n===== Excel File Combination Tool =====\n")
    print(f"Python version: {sys.version}")
    try:
        print(f"Pandas version: {pd.__version__}")
        import openpyxl
        print(f"Openpyxl version: {openpyxl.__version__}")
    except ImportError as e:
        print(f"ERROR: Missing required library: {str(e)}")
        print("Please install required libraries with: pip install pandas openpyxl")
        sys.exit(1)
    
    print("\nStarting process...\n")
    main()
    print("\n===== Process Complete =====\n")