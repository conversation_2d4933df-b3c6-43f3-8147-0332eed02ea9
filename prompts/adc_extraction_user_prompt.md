Extract list of Antibody-Drug Conjugate (ADC) and their components.
Rules:
- Use exact enum values (e.g., "IGG", not "IgG")
- For non-standard values, use "OTHER" with specification
- Include all required fields, use "Not specified" for missing data

Text to analyze:
{{TEXT}}

CRITICAL REQUIREMENT: Extract all ADCs as a list of AntibodyDrugConjugate objects inside a single list. Do not extract multiple lists, have all ADCs in a single list only.
