#!/usr/bin/env python3
"""
Script to process top_articles.parquet and top_reviews.parquet files
and extract XML content according to specified criteria.
"""

import pandas as pd
import os
from pathlib import Path

def examine_parquet_structure(file_path):
    """Examine the structure of a parquet file"""
    print(f"\n=== Examining {file_path} ===")
    df = pd.read_parquet(file_path)
    print(f"Shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
    print(f"\nData types:")
    print(df.dtypes)
    print(f"\nFirst few rows:")
    print(df.head())
    print(f"\nSummary statistics for numeric columns:")
    print(df.describe())
    return df

def process_articles(df_articles):
    """Process top_articles.parquet according to specifications"""
    print("\n=== Processing Articles ===")
    
    # Step 1: Sort by fwci in descending order
    df_sorted = df_articles.sort_values('fwci', ascending=False)
    print(f"Sorted by fwci (descending). Top fwci value: {df_sorted['fwci'].iloc[0]}")
    
    # Step 2: Take top 150 records by fwci
    df_top_150 = df_sorted.head(150)
    print(f"Selected top 150 records by fwci")
    
    # Step 3: From those 150, get top 60 by ans_endpoints_count
    df_final = df_top_150.sort_values('ans_endpoints_count', ascending=False).head(60)
    print(f"Selected top 60 from those 150 by ans_endpoints_count")
    print(f"Final selection - fwci range: {df_final['fwci'].min():.3f} to {df_final['fwci'].max():.3f}")
    print(f"Final selection - ans_endpoints_count range: {df_final['ans_endpoints_count'].min()} to {df_final['ans_endpoints_count'].max()}")
    
    return df_final

def process_reviews(df_reviews):
    """Process top_reviews.parquet according to specifications"""
    print("\n=== Processing Reviews ===")
    
    # Step 1: Sort by fwci in descending order
    df_sorted = df_reviews.sort_values('fwci', ascending=False)
    print(f"Sorted by fwci (descending). Top fwci value: {df_sorted['fwci'].iloc[0]}")
    
    # Step 2: Take top 40 records by fwci
    df_final = df_sorted.head(40)
    print(f"Selected top 40 records by fwci")
    print(f"Final selection - fwci range: {df_final['fwci'].min():.3f} to {df_final['fwci'].max():.3f}")
    
    return df_final

def save_xml_files(df, output_dir, prefix=""):
    """Save rawContent (XML) to individual files using work IDs as filenames"""
    # Create output directory
    Path(output_dir).mkdir(exist_ok=True)

    saved_count = 0
    for _, row in df.iterrows():
        if pd.notna(row['rawContent']) and row['rawContent'].strip():
            # Use paper_id (work ID) as the filename
            work_id = row['paper_id']
            filename = f"{prefix}{work_id}.xml"
            filepath = Path(output_dir) / filename

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(row['rawContent'])
            saved_count += 1

    print(f"Saved {saved_count} XML files to {output_dir}")
    return saved_count

def main():
    """Main processing function"""
    print("Starting parquet file processing...")
    
    # Check if files exist
    articles_file = "top_articles.parquet"
    reviews_file = "top_reviews.parquet"
    
    if not os.path.exists(articles_file):
        print(f"Error: {articles_file} not found!")
        return
    
    if not os.path.exists(reviews_file):
        print(f"Error: {reviews_file} not found!")
        return
    
    # Examine file structures
    df_articles = examine_parquet_structure(articles_file)
    df_reviews = examine_parquet_structure(reviews_file)
    
    # Process articles
    final_articles = process_articles(df_articles)
    
    # Process reviews
    final_reviews = process_reviews(df_reviews)
    
    # Remove existing directories if they exist
    import shutil
    if os.path.exists("xml_articles"):
        shutil.rmtree("xml_articles")
    if os.path.exists("xml_reviews"):
        shutil.rmtree("xml_reviews")

    # Save XML files
    articles_saved = save_xml_files(final_articles, "xml_articles", "")
    reviews_saved = save_xml_files(final_reviews, "xml_reviews", "")
    
    print(f"\n=== Summary ===")
    print(f"Articles processed: {len(final_articles)} records, {articles_saved} XML files saved")
    print(f"Reviews processed: {len(final_reviews)} records, {reviews_saved} XML files saved")

if __name__ == "__main__":
    main()
